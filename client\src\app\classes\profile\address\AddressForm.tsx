'use client';

import { useState, useEffect, useRef } from 'react';
import { axiosInstance } from '@/lib/axios';
import { Button } from '@/components/ui/button';
import { useDispatch, useSelector } from 'react-redux';
import { fetchClassDetails } from '@/store/thunks/classThunks';
import { completeForm, FormId } from '@/store/slices/formProgressSlice';
import { toast } from 'sonner';
import { RootState } from '@/store';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

declare global {
  interface Window {
    google: any;
  }
}

type SelectedPlace = {
  address: string;
  city: string | null;
  state: string | null;
  postcode: string | null;
  country: string | null;
  latitude: number;
  longitude: number;
};

const AddressAutocomplete = () => {
  const [address, setAddress] = useState<string>('');
  const autocompleteInputRef = useRef<HTMLInputElement | null>(null);
  const [selectedPlace, setSelectedPlace] = useState<SelectedPlace | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [existingAddress, setExistingAddress] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const dispatch = useDispatch();
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const classData = useSelector((state: RootState) => state.class.classData);

  // Function to parse address components from Google Maps place
  const parseAddressComponents = (place: any): SelectedPlace => {
    const addressComponents = place.address_components || [];
    let city: string | null = null;
    let state: string | null = null;
    let postcode: string | null = null;
    let country: string | null = null;

    addressComponents.forEach((component: any) => {
      const types = component.types;
      if (types.includes('locality') || types.includes('sublocality')) {
        city = component.long_name;
      }
      if (types.includes('administrative_area_level_1')) {
        state = component.long_name;
      }
      if (types.includes('postal_code')) {
        postcode = component.long_name;
      }
      if (types.includes('country')) {
        country = component.long_name;
      }
    });

    return {
      address: place.formatted_address,
      city,
      state,
      postcode,
      country,
      latitude: place.geometry.location.lat(),
      longitude: place.geometry.location.lng(),
    };
  };

  const onPlaceSelected = (place: SelectedPlace) => {
    setSelectedPlace(place);
  };

  const saveAddressToServer = async () => {
    if (!selectedPlace) return;

    try {
      setIsSaving(true);
      setSuccessMessage('');

      const res = await axiosInstance.post('/classes-profile/address', {
        fullAddress: selectedPlace.address,
        city: selectedPlace.city,
        state: selectedPlace.state,
        postcode: selectedPlace.postcode,
        country: selectedPlace.country,
        latitude: selectedPlace.latitude,
        longitude: selectedPlace.longitude,
        classId: classData?.id, // Assuming classData has the classId
      });

      if (res.data?.success) {
        await dispatch(fetchClassDetails(user.id));
        toast.success('Address saved successfully');
        dispatch(completeForm(FormId.ADDRESS));
        setSuccessMessage('Address saved successfully.');
      }
    } catch (error) {
      console.error('Error saving address:', error);
      setSuccessMessage('Failed to save address.');
    } finally {
      setIsSaving(false);
    }
  };

  useEffect(() => {
    const loadScript = () => {
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`;
      script.async = true;
      script.defer = true;
      document.head.appendChild(script);
      script.onload = initializeAutocomplete;
    };

    const initializeAutocomplete = () => {
      if (window.google && window.google.maps && autocompleteInputRef.current) {
        const autocomplete = new window.google.maps.places.Autocomplete(autocompleteInputRef.current, {
          types: ['geocode', 'establishment'],
          componentRestrictions: { country: 'in' }, // Restrict to India for better accuracy
        });

         autocomplete.addListener('place_changed', () => {
          const place = autocomplete.getPlace();
          if (place.geometry && place.formatted_address) {
            const selected = parseAddressComponents(place);
            setAddress(place.formatted_address);
            onPlaceSelected(selected);
          }
        });
      }
    };

    if (!window.google || !window.google.maps) {
      loadScript();
    } else {
      initializeAutocomplete();
    }

    return () => {
      const scripts = document.querySelectorAll('script[src*="maps.googleapis.com"]');
      scripts.forEach((script) => script.remove());
    };
  }, []);

  return (
    <div className="w-full max-w-md">
      {/* Display Existing Address from Redux */}
      {classData?.address && (
        <div className="space-y-4 mb-6">
          <h3 className="text-lg font-semibold">Previous Address</h3>
          <Card className="bg-muted/30">
            <CardHeader>
              <CardTitle className="text-base font-semibold">Saved Address</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <span className="font-medium">Address:</span> {classData.address.fullAddress}
              </div>
              <div>
                <span className="font-medium">City:</span> {classData.address.city || 'N/A'}
              </div>
              <div>
                <span className="font-medium">State:</span> {classData.address.state || 'N/A'}
              </div>
              <div>
                <span className="font-medium">Postcode:</span> {classData.address.postcode || 'N/A'}
              </div>
              <div>
                <span className="font-medium">Country:</span> {classData.address.country || 'N/A'}
              </div>
              <div>
                <span className="font-medium">Latitude:</span> {classData.address.latitude}
              </div>
              <div>
                <span className="font-medium">Longitude:</span> {classData.address.longitude}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Address Input */}
      <label htmlFor="address" className="block text-sm font-medium text-gray-700">
        Address
      </label>
      <input
        id="address"
        type="text"
        ref={autocompleteInputRef}
        value={address}
        onChange={(e) => setAddress(e.target.value)}
        placeholder="Enter your address"
        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
      />

      {/* Display Selected Place */}
      {selectedPlace && (
        <div className="mt-4 p-4 bg-gray-50 rounded-md">
          <h2 className="text-lg font-semibold">Selected Place</h2>
          <p><strong>Address:</strong> {selectedPlace.address}</p>
          <p><strong>City:</strong> {selectedPlace.city || 'N/A'}</p>
          <p><strong>State:</strong> {selectedPlace.state || 'N/A'}</p>
          <p><strong>Postcode:</strong> {selectedPlace.postcode || 'N/A'}</p>
          <p><strong>Country:</strong> {selectedPlace.country || 'N/A'}</p>
          <p><strong>Latitude:</strong> {selectedPlace.latitude}</p>
          <p><strong>Longitude:</strong> {selectedPlace.longitude}</p>

          <Button
            onClick={saveAddressToServer}
            className="mt-4 px-4 py-2"
            disabled={isSaving}
          >
            {isSaving ? 'Saving...' : 'Save Address'}
          </Button>

          {successMessage && (
            <p className="mt-2 text-sm text-green-600">{successMessage}</p>
          )}
        </div>
      )}
    </div>
  );
};

export default AddressAutocomplete;