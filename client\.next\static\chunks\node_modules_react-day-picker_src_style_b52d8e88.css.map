{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/react-day-picker/src/style.css"], "sourcesContent": ["/* Variables declaration */\n/* prettier-ignore */\n.rdp-root {\n  --rdp-accent-color: blue; /* The accent color used for selected days and UI elements. */\n  --rdp-accent-background-color: #f0f0ff; /* The accent background color used for selected days and UI elements. */\n\n  --rdp-day-height: 44px; /* The height of the day cells. */\n  --rdp-day-width: 44px; /* The width of the day cells. */\n  \n  --rdp-day_button-border-radius: 100%; /* The border radius of the day cells. */\n  --rdp-day_button-border: 2px solid transparent; /* The border of the day cells. */\n  --rdp-day_button-height: 42px; /* The height of the day cells. */\n  --rdp-day_button-width: 42px; /* The width of the day cells. */\n  \n  --rdp-selected-border: 2px solid var(--rdp-accent-color); /* The border of the selected days. */\n  --rdp-disabled-opacity: 0.5; /* The opacity of the disabled days. */\n  --rdp-outside-opacity: 0.75; /* The opacity of the days outside the current month. */\n  --rdp-today-color: var(--rdp-accent-color); /* The color of the today's date. */\n  \n  --rdp-dropdown-gap: 0.5rem;/* The gap between the dropdowns used in the month captons. */\n  \n  --rdp-months-gap: 2rem; /* The gap between the months in the multi-month view. */\n  \n  --rdp-nav_button-disabled-opacity: 0.5; /* The opacity of the disabled navigation buttons. */\n  --rdp-nav_button-height: 2.25rem; /* The height of the navigation buttons. */\n  --rdp-nav_button-width: 2.25rem; /* The width of the navigation buttons. */\n  --rdp-nav-height: 2.75rem; /* The height of the navigation bar. */\n  \n  --rdp-range_middle-background-color: var(--rdp-accent-background-color); /* The color of the background for days in the middle of a range. */\n  --rdp-range_middle-color: inherit;/* The color of the range text. */\n  \n  --rdp-range_start-color: white; /* The color of the range text. */\n  --rdp-range_start-background: linear-gradient(var(--rdp-gradient-direction), transparent 50%, var(--rdp-range_middle-background-color) 50%); /* Used for the background of the start of the selected range. */\n  --rdp-range_start-date-background-color: var(--rdp-accent-color); /* The background color of the date when at the start of the selected range. */\n  \n  --rdp-range_end-background: linear-gradient(var(--rdp-gradient-direction), var(--rdp-range_middle-background-color) 50%, transparent 50%); /* Used for the background of the end of the selected range. */\n  --rdp-range_end-color: white;/* The color of the range text. */\n  --rdp-range_end-date-background-color: var(--rdp-accent-color); /* The background color of the date when at the end of the selected range. */\n  \n  --rdp-week_number-border-radius: 100%; /* The border radius of the week number. */\n  --rdp-week_number-border: 2px solid transparent; /* The border of the week number. */\n  \n  --rdp-week_number-height: var(--rdp-day-height); /* The height of the week number cells. */\n  --rdp-week_number-opacity: 0.75; /* The opacity of the week number. */\n  --rdp-week_number-width: var(--rdp-day-width); /* The width of the week number cells. */\n  --rdp-weeknumber-text-align: center; /* The text alignment of the weekday cells. */\n\n  --rdp-weekday-opacity: 0.75; /* The opacity of the weekday. */\n  --rdp-weekday-padding: 0.5rem 0rem; /* The padding of the weekday. */\n  --rdp-weekday-text-align: center; /* The text alignment of the weekday cells. */\n\n  --rdp-gradient-direction: 90deg;\n\n  --rdp-animation_duration: 0.3s;\n  --rdp-animation_timing: cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.rdp-root[dir=\"rtl\"] {\n  --rdp-gradient-direction: -90deg;\n}\n\n.rdp-root[data-broadcast-calendar=\"true\"] {\n  --rdp-outside-opacity: unset;\n}\n\n/* Root of the component. */\n.rdp-root {\n  position: relative; /* Required to position the navigation toolbar. */\n  box-sizing: border-box;\n}\n\n.rdp-root * {\n  box-sizing: border-box;\n}\n\n.rdp-day {\n  width: var(--rdp-day-width);\n  height: var(--rdp-day-height);\n  text-align: center;\n}\n\n.rdp-day_button {\n  background: none;\n  padding: 0;\n  margin: 0;\n  cursor: pointer;\n  font: inherit;\n  color: inherit;\n  justify-content: center;\n  align-items: center;\n  display: flex;\n\n  width: var(--rdp-day_button-width);\n  height: var(--rdp-day_button-height);\n  border: var(--rdp-day_button-border);\n  border-radius: var(--rdp-day_button-border-radius);\n}\n\n.rdp-day_button:disabled {\n  cursor: revert;\n}\n\n.rdp-caption_label {\n  z-index: 1;\n\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n\n  white-space: nowrap;\n  border: 0;\n}\n\n.rdp-dropdown:focus-visible ~ .rdp-caption_label {\n  outline: 5px auto Highlight;\n  outline: 5px auto -webkit-focus-ring-color;\n}\n\n.rdp-button_next,\n.rdp-button_previous {\n  border: none;\n  background: none;\n  padding: 0;\n  margin: 0;\n  cursor: pointer;\n  font: inherit;\n  color: inherit;\n  -moz-appearance: none;\n  -webkit-appearance: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  appearance: none;\n\n  width: var(--rdp-nav_button-width);\n  height: var(--rdp-nav_button-height);\n}\n\n.rdp-button_next:disabled,\n.rdp-button_next[aria-disabled=\"true\"],\n.rdp-button_previous:disabled,\n.rdp-button_previous[aria-disabled=\"true\"] {\n  cursor: revert;\n\n  opacity: var(--rdp-nav_button-disabled-opacity);\n}\n\n.rdp-chevron {\n  display: inline-block;\n  fill: var(--rdp-accent-color);\n}\n\n.rdp-root[dir=\"rtl\"] .rdp-nav .rdp-chevron {\n  transform: rotate(180deg);\n  transform-origin: 50%;\n}\n\n.rdp-dropdowns {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  gap: var(--rdp-dropdown-gap);\n}\n.rdp-dropdown {\n  z-index: 2;\n\n  /* Reset */\n  opacity: 0;\n  appearance: none;\n  position: absolute;\n  inset-block-start: 0;\n  inset-block-end: 0;\n  inset-inline-start: 0;\n  width: 100%;\n  margin: 0;\n  padding: 0;\n  cursor: inherit;\n  border: none;\n  line-height: inherit;\n}\n\n.rdp-dropdown_root {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n}\n\n.rdp-dropdown_root[data-disabled=\"true\"] .rdp-chevron {\n  opacity: var(--rdp-disabled-opacity);\n}\n\n.rdp-month_caption {\n  display: flex;\n  align-content: center;\n  height: var(--rdp-nav-height);\n  font-weight: bold;\n  font-size: large;\n}\n\n.rdp-root[data-nav-layout=\"around\"] .rdp-month,\n.rdp-root[data-nav-layout=\"after\"] .rdp-month {\n  position: relative;\n}\n\n.rdp-root[data-nav-layout=\"around\"] .rdp-month_caption {\n  justify-content: center;\n  margin-inline-start: var(--rdp-nav_button-width);\n  margin-inline-end: var(--rdp-nav_button-width);\n  position: relative;\n}\n\n.rdp-root[data-nav-layout=\"around\"] .rdp-button_previous {\n  position: absolute;\n  inset-inline-start: 0;\n  top: 0;\n  height: var(--rdp-nav-height);\n  display: inline-flex;\n}\n\n.rdp-root[data-nav-layout=\"around\"] .rdp-button_next {\n  position: absolute;\n  inset-inline-end: 0;\n  top: 0;\n  height: var(--rdp-nav-height);\n  display: inline-flex;\n  justify-content: center;\n}\n\n.rdp-months {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  gap: var(--rdp-months-gap);\n  max-width: fit-content;\n}\n\n.rdp-month_grid {\n  border-collapse: collapse;\n}\n\n.rdp-nav {\n  position: absolute;\n  inset-block-start: 0;\n  inset-inline-end: 0;\n\n  display: flex;\n  align-items: center;\n\n  height: var(--rdp-nav-height);\n}\n\n.rdp-weekday {\n  opacity: var(--rdp-weekday-opacity);\n  padding: var(--rdp-weekday-padding);\n  font-weight: 500;\n  font-size: smaller;\n  text-align: var(--rdp-weekday-text-align);\n  text-transform: var(--rdp-weekday-text-transform);\n}\n\n.rdp-week_number {\n  opacity: var(--rdp-week_number-opacity);\n  font-weight: 400;\n  font-size: small;\n  height: var(--rdp-week_number-height);\n  width: var(--rdp-week_number-width);\n  border: var(--rdp-week_number-border);\n  border-radius: var(--rdp-week_number-border-radius);\n  text-align: var(--rdp-weeknumber-text-align);\n}\n\n/* DAY MODIFIERS */\n.rdp-today:not(.rdp-outside) {\n  color: var(--rdp-today-color);\n}\n\n.rdp-selected {\n  font-weight: bold;\n  font-size: large;\n}\n\n.rdp-selected .rdp-day_button {\n  border: var(--rdp-selected-border);\n}\n\n.rdp-outside {\n  opacity: var(--rdp-outside-opacity);\n}\n\n.rdp-disabled {\n  opacity: var(--rdp-disabled-opacity);\n}\n\n.rdp-hidden {\n  visibility: hidden;\n  color: var(--rdp-range_start-color);\n}\n\n.rdp-range_start {\n  background: var(--rdp-range_start-background);\n}\n\n.rdp-range_start .rdp-day_button {\n  background-color: var(--rdp-range_start-date-background-color);\n  color: var(--rdp-range_start-color);\n}\n\n.rdp-range_middle {\n  background-color: var(--rdp-range_middle-background-color);\n}\n\n.rdp-range_middle .rdp-day_button {\n  border-color: transparent;\n  border: unset;\n  border-radius: unset;\n  color: var(--rdp-range_middle-color);\n}\n\n.rdp-range_end {\n  background: var(--rdp-range_end-background);\n  color: var(--rdp-range_end-color);\n}\n\n.rdp-range_end .rdp-day_button {\n  color: var(--rdp-range_start-color);\n  background-color: var(--rdp-range_end-date-background-color);\n}\n\n.rdp-range_start.rdp-range_end {\n  background: revert;\n}\n\n.rdp-focusable {\n  cursor: pointer;\n}\n\n@keyframes rdp-slide_in_left {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(0);\n  }\n}\n\n@keyframes rdp-slide_in_right {\n  0% {\n    transform: translateX(100%);\n  }\n  100% {\n    transform: translateX(0);\n  }\n}\n\n@keyframes rdp-slide_out_left {\n  0% {\n    transform: translateX(0);\n  }\n  100% {\n    transform: translateX(-100%);\n  }\n}\n\n@keyframes rdp-slide_out_right {\n  0% {\n    transform: translateX(0);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n.rdp-weeks_before_enter {\n  animation: rdp-slide_in_left var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-weeks_before_exit {\n  animation: rdp-slide_out_left var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-weeks_after_enter {\n  animation: rdp-slide_in_right var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-weeks_after_exit {\n  animation: rdp-slide_out_right var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-root[dir=\"rtl\"] .rdp-weeks_after_enter {\n  animation: rdp-slide_in_left var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-root[dir=\"rtl\"] .rdp-weeks_before_exit {\n  animation: rdp-slide_out_right var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-root[dir=\"rtl\"] .rdp-weeks_before_enter {\n  animation: rdp-slide_in_right var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-root[dir=\"rtl\"] .rdp-weeks_after_exit {\n  animation: rdp-slide_out_left var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n@keyframes rdp-fade_in {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes rdp-fade_out {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n.rdp-caption_after_enter {\n  animation: rdp-fade_in var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-caption_after_exit {\n  animation: rdp-fade_out var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-caption_before_enter {\n  animation: rdp-fade_in var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-caption_before_exit {\n  animation: rdp-fade_out var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n"], "names": [], "mappings": "AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDA;;;;AAIA;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;;;;;;;;;;;;AAqBA;;;;;AASA;;;;;AAKA;;;;;AAKA;;;;;;;AAMA;;;;;;;;;;;;;;;;AAkBA;;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;AAKA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;;;;;;AAYA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;AAKA;;;;AAKA;;;;AAKA", "ignoreList": [0]}}]}